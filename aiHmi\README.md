# AI-HMI 智能交互界面

一个基于 Framework7 + Vue 3 + Tailwind CSS + p5.js 技术栈的交互式转盘菜单应用。

## 🚀 功能特性

### 🎯 核心功能
- **交互式转盘菜单**: 可点击、拖拽的圆形菜单布局
- **物理动画效果**: 缓动动画、惯性滑动、弹簧效果
- **智能旋转**: 点击菜单项自动旋转到12点钟位置
- **拖拽交互**: 支持鼠标拖拽旋转转盘
- **悬停效果**: 鼠标悬停时的视觉反馈

### 🎨 视觉设计
- **科技感UI**: 霓虹蓝色主题，发光效果
- **动态中心**: 机器人图片配合多层旋转光环
- **响应式设计**: 适配手机、平板、桌面设备
- **实时状态**: 显示系统状态和当前时间
- **流畅动画**: 60fps流畅动画体验

### 📱 响应式适配
- **移动设备**: 320px - 768px
- **平板设备**: 768px - 1024px (优化适配)
- **桌面设备**: 1024px+

## 🛠️ 技术栈

- **前端框架**: Vue 3
- **UI框架**: Framework7
- **样式框架**: Tailwind CSS v3.4
- **动画引擎**: p5.js
- **构建工具**: Vite
- **图标库**: Font Awesome v5.11.2

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:3000

### 构建生产版本
```bash
npm run build
```

## 🎮 使用说明

### 基本操作
1. **点击菜单项**: 直接点击转盘上的任意菜单项
2. **拖拽旋转**: 在转盘区域拖拽鼠标来旋转转盘
3. **自动对齐**: 选中的菜单项会自动旋转到顶部位置

### 菜单项目
- 🚗 智车导航
- 🍽️ 美食导航  
- 🗺️ 地图导航
- 🎵 听我说话
- 🎓 亲子学堂
- ⚙️ 专属配置
- 🛡️ 专属配置
- 💬 听我说话

### 交互反馈
- 悬停时菜单项会放大并发光
- 选中后中心机器人会有动画反馈
- 系统状态实时显示在左上角
- 当前时间显示在右上角

## 🎯 项目结构

```
aiHmi/
├── src/
│   ├── components/
│   │   └── WheelMenu.vue      # 转盘菜单核心组件
│   ├── views/
│   │   └── Home.vue           # 主页视图
│   ├── router/
│   │   └── index.js           # 路由配置
│   ├── style.css              # 全局样式
│   ├── App.vue                # 根组件
│   └── main.js                # 入口文件
├── public/                    # 静态资源
├── index.html                 # HTML模板
├── package.json               # 项目配置
├── vite.config.js            # Vite配置
├── tailwind.config.js        # Tailwind配置
└── postcss.config.js         # PostCSS配置
```

## 🔧 自定义配置

### 修改菜单项
在 `src/components/WheelMenu.vue` 中修改 `menuItems` 数组：

```javascript
menuItems: [
  { icon: 'fas fa-car', label: '智车导航', color: '#00d4ff', bgColor: 'rgba(0, 212, 255, 0.2)' },
  // 添加更多菜单项...
]
```

### 调整动画参数
在组件的 `data` 中修改物理参数：

```javascript
damping: 0.95,        // 阻尼系数 (0-1)
springStrength: 0.15, // 弹簧强度 (0-1)
```

### 自定义颜色主题
在 `tailwind.config.js` 中修改颜色配置：

```javascript
colors: {
  'tech-blue': '#00d4ff',
  'tech-purple': '#6366f1',
  // 添加更多颜色...
}
```

## 🌟 特色亮点

1. **流畅的物理动画**: 使用弹簧动力学和阻尼系统
2. **智能交互检测**: 区分点击和拖拽操作
3. **响应式p5.js画布**: 自适应屏幕尺寸
4. **多层视觉效果**: 光环、发光、脉冲等效果
5. **实时状态反馈**: 动态显示系统状态

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**AI-HMI** - 让科技改变生活 🚀
