<template>
  <div class="wheel-container">
    <!-- p5.js 画布将在这里渲染 -->
    <div ref="p5Container" class="relative w-full h-full"></div>
    
    <!-- 中心机器人 -->
    <div class="robot-center" :class="{ 'animate-pulse': isAnimating }">
      <div class="relative">
        <!-- 机器人GIF图片 -->
        <img
          src="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=200&h=200&fit=crop&crop=center"
          alt="AI Robot"
          class="w-32 h-32 rounded-full animate-float tech-glow transition-all duration-300"
          :class="{ 'scale-110': selectedItem }"
        />

        <!-- 多层旋转光环 -->
        <div class="absolute inset-0 w-32 h-32 border-2 border-tech-blue rounded-full animate-spin-slow opacity-30"
             :style="{ animationDuration: isAnimating ? '1s' : '3s' }"></div>
        <div class="absolute inset-2 w-28 h-28 border border-neon-purple rounded-full animate-spin-slow opacity-50"
             :style="{ animationDirection: 'reverse', animationDuration: isAnimating ? '0.8s' : '2.5s' }"></div>
        <div class="absolute inset-4 w-24 h-24 border border-tech-blue rounded-full animate-spin-slow opacity-20"
             :style="{ animationDuration: isAnimating ? '1.2s' : '4s' }"></div>

        <!-- 能量脉冲效果 -->
        <div v-if="isAnimating" class="absolute inset-0 w-32 h-32 bg-tech-blue rounded-full opacity-10 animate-ping"></div>

        <!-- 状态指示器 -->
        <div class="absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center"
             :class="isAnimating ? 'bg-green-500 animate-pulse' : 'bg-blue-500'">
          <div class="w-2 h-2 bg-white rounded-full"></div>
        </div>
      </div>

      <div class="text-center mt-4">
        <h2 class="text-2xl font-bold neon-text transition-all duration-300"
            :class="{ 'text-green-400': selectedItem }">AI-HMI</h2>
        <p class="text-sm text-tech-blue opacity-75 mt-1 transition-all duration-300">
          {{ selectedItem ? `已选择: ${selectedItem.label}` : '智能交互系统' }}
        </p>

        <!-- 活动指示器 -->
        <div class="flex justify-center mt-2 space-x-1">
          <div v-for="i in 3" :key="i"
               class="w-2 h-2 rounded-full transition-all duration-300"
               :class="isAnimating ? 'bg-tech-blue animate-pulse' : 'bg-gray-600'"
               :style="{ animationDelay: `${i * 0.2}s` }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import p5 from 'p5'

export default {
  name: 'WheelMenu',
  data() {
    return {
      p5Instance: null,
      menuItems: [
        { icon: 'fas fa-car', label: '智车导航', color: '#00d4ff', bgColor: 'rgba(0, 212, 255, 0.2)' },
        { icon: 'fas fa-utensils', label: '美食导航', color: '#ff6b6b', bgColor: 'rgba(255, 107, 107, 0.2)' },
        { icon: 'fas fa-map', label: '地图导航', color: '#4ecdc4', bgColor: 'rgba(78, 205, 196, 0.2)' },
        { icon: 'fas fa-music', label: '听我说话', color: '#45b7d1', bgColor: 'rgba(69, 183, 209, 0.2)' },
        { icon: 'fas fa-graduation-cap', label: '亲子学堂', color: '#96ceb4', bgColor: 'rgba(150, 206, 180, 0.2)' },
        { icon: 'fas fa-cog', label: '专属配置', color: '#feca57', bgColor: 'rgba(254, 202, 87, 0.2)' },
        { icon: 'fas fa-shield-alt', label: '专属配置', color: '#ff9ff3', bgColor: 'rgba(255, 159, 243, 0.2)' },
        { icon: 'fas fa-comments', label: '听我说话', color: '#54a0ff', bgColor: 'rgba(84, 160, 255, 0.2)' }
      ],
      currentRotation: 0,
      targetRotation: 0,
      isAnimating: false,
      hoveredIndex: -1,
      velocity: 0,
      damping: 0.95,
      springStrength: 0.15,
      isDragging: false,
      lastMouseAngle: 0,
      dragStartAngle: 0,
      selectedItem: null
    }
  },
  mounted() {
    this.initP5()
  },
  beforeUnmount() {
    if (this.p5Instance) {
      this.p5Instance.remove()
    }
  },

  watch: {
    // 监听窗口大小变化，确保响应式
    '$route'() {
      this.$nextTick(() => {
        if (this.p5Instance) {
          this.p5Instance.resizeCanvas(window.innerWidth, window.innerHeight)
        }
      })
    }
  },
  methods: {
    initP5() {
      const sketch = (p) => {
        let radius, centerX, centerY, itemRadius

        const updateDimensions = () => {
          // 响应式尺寸计算
          const minDimension = Math.min(p.windowWidth, p.windowHeight)
          radius = minDimension * 0.35 // 35% of smaller dimension
          centerX = p.windowWidth / 2
          centerY = p.windowHeight / 2
          itemRadius = Math.max(40, minDimension * 0.08) // 最小40px，或8%的屏幕尺寸
        }
        
        p.setup = () => {
          p.createCanvas(p.windowWidth, p.windowHeight)
          p.angleMode(p.DEGREES)
          updateDimensions()
        }
        
        p.draw = () => {
          p.clear()

          // 绘制背景光环
          p.push()
          p.translate(centerX, centerY)
          p.noFill()
          p.stroke(0, 212, 255, 30)
          p.strokeWeight(1)
          p.circle(0, 0, radius * 2.2)
          p.stroke(139, 92, 246, 20)
          p.circle(0, 0, radius * 2.4)
          p.pop()

          // 绘制转盘内容
          p.push()
          p.translate(centerX, centerY)
          p.rotate(this.currentRotation)

          // 绘制连接线和光效
          for (let i = 0; i < this.menuItems.length; i++) {
            const angle = (360 / this.menuItems.length) * i
            const x = p.cos(angle) * radius
            const y = p.sin(angle) * radius

            // 连接线
            p.stroke(0, 212, 255, 60)
            p.strokeWeight(1)
            p.line(0, 0, x, y)

            // 光效线
            p.stroke(0, 212, 255, 20)
            p.strokeWeight(3)
            p.line(x * 0.7, y * 0.7, x * 1.1, y * 1.1)
          }

          // 绘制菜单项
          for (let i = 0; i < this.menuItems.length; i++) {
            const angle = (360 / this.menuItems.length) * i
            const x = p.cos(angle) * radius
            const y = p.sin(angle) * radius
            const item = this.menuItems[i]
            const isHovered = this.hoveredIndex === i
            const scale = isHovered ? 1.1 : 1

            p.push()
            p.translate(x, y)
            p.scale(scale)

            // 绘制发光背景
            if (isHovered) {
              p.fill(p.red(item.color), p.green(item.color), p.blue(item.color), 30)
              p.noStroke()
              p.circle(0, 0, itemRadius * 1.5)
            }

            // 绘制菜单项背景
            p.fill(30, 41, 59, 220)
            p.stroke(p.red(item.color), p.green(item.color), p.blue(item.color), 180)
            p.strokeWeight(2)
            p.circle(0, 0, itemRadius)

            // 绘制内圈
            p.fill(p.red(item.color), p.green(item.color), p.blue(item.color), 100)
            p.noStroke()
            p.circle(0, 0, itemRadius * 0.7)

            // 绘制图标占位符（简化的几何形状）
            p.fill(255, 255, 255, 200)
            p.noStroke()
            this.drawIconShape(p, item.icon, itemRadius * 0.3)

            p.pop()
          }

          p.pop()
          
          // 更新物理动画
          if (this.isAnimating || Math.abs(this.velocity) > 0.1) {
            if (this.isAnimating) {
              // 弹簧动画到目标位置
              const diff = this.targetRotation - this.currentRotation
              const force = diff * this.springStrength
              this.velocity += force
              this.velocity *= this.damping

              if (Math.abs(diff) < 0.5 && Math.abs(this.velocity) < 0.5) {
                this.currentRotation = this.targetRotation
                this.velocity = 0
                this.isAnimating = false
              }
            } else {
              // 自由惯性滑动
              this.velocity *= this.damping
              if (Math.abs(this.velocity) < 0.1) {
                this.velocity = 0
              }
            }

            this.currentRotation += this.velocity
          }
        }
        
        p.mouseMoved = () => {
          const mouseX = p.mouseX - centerX
          const mouseY = p.mouseY - centerY
          const distance = p.dist(0, 0, mouseX, mouseY)

          this.hoveredIndex = -1

          if (distance > radius - itemRadius && distance < radius + itemRadius) {
            // 计算鼠标悬停的角度
            let angle = p.atan2(mouseY, mouseX)
            if (angle < 0) angle += 360

            // 调整角度考虑当前旋转
            angle = (angle - this.currentRotation + 360) % 360

            // 确定悬停的菜单项
            const itemAngle = 360 / this.menuItems.length
            this.hoveredIndex = Math.round(angle / itemAngle) % this.menuItems.length
          }
        }

        p.mousePressed = () => {
          const mouseX = p.mouseX - centerX
          const mouseY = p.mouseY - centerY
          const distance = p.dist(0, 0, mouseX, mouseY)

          if (distance > radius - itemRadius * 1.5 && distance < radius + itemRadius * 1.5) {
            this.isDragging = true
            this.isAnimating = false
            this.velocity = 0

            // 记录开始拖拽的角度
            this.dragStartAngle = p.atan2(mouseY, mouseX)
            this.lastMouseAngle = this.dragStartAngle
          }
        }

        p.mouseDragged = () => {
          if (this.isDragging) {
            const mouseX = p.mouseX - centerX
            const mouseY = p.mouseY - centerY
            const currentAngle = p.atan2(mouseY, mouseX)

            // 计算角度差
            let angleDiff = currentAngle - this.lastMouseAngle

            // 处理角度跨越边界的情况
            if (angleDiff > p.PI) angleDiff -= p.TWO_PI
            if (angleDiff < -p.PI) angleDiff += p.TWO_PI

            // 更新旋转角度
            this.currentRotation += p.degrees(angleDiff)
            this.velocity = p.degrees(angleDiff) * 0.8 // 设置拖拽速度

            this.lastMouseAngle = currentAngle
          }
        }

        p.mouseReleased = () => {
          if (this.isDragging) {
            this.isDragging = false

            // 如果拖拽距离很小，认为是点击
            const totalDrag = Math.abs(this.lastMouseAngle - this.dragStartAngle)
            if (totalDrag < 0.1) {
              const mouseX = p.mouseX - centerX
              const mouseY = p.mouseY - centerY

              // 计算点击的角度
              let angle = p.atan2(mouseY, mouseX)
              if (angle < 0) angle += 360

              // 调整角度考虑当前旋转
              angle = (angle - this.currentRotation + 360) % 360

              // 确定点击的菜单项
              const itemAngle = 360 / this.menuItems.length
              const clickedIndex = Math.round(angle / itemAngle) % this.menuItems.length

              this.selectMenuItem(clickedIndex)
            }
          }
        }
        
        p.windowResized = () => {
          p.resizeCanvas(p.windowWidth, p.windowHeight)
          updateDimensions()
        }
      }
      
      this.p5Instance = new p5(sketch, this.$refs.p5Container)
    },
    
    selectMenuItem(index) {
      // 计算需要旋转的角度，使选中的项目移动到顶部（-90度位置）
      const itemAngle = 360 / this.menuItems.length
      let targetAngle = -90 - (index * itemAngle)

      // 找到最短的旋转路径
      const currentNormalized = this.currentRotation % 360
      const diff1 = targetAngle - currentNormalized
      const diff2 = diff1 + 360
      const diff3 = diff1 - 360

      // 选择绝对值最小的差值
      let shortestDiff = diff1
      if (Math.abs(diff2) < Math.abs(shortestDiff)) shortestDiff = diff2
      if (Math.abs(diff3) < Math.abs(shortestDiff)) shortestDiff = diff3

      this.targetRotation = this.currentRotation + shortestDiff
      this.isAnimating = true
      this.velocity = 0
      this.selectedItem = this.menuItems[index]

      // 触发选择事件
      this.$emit('menu-selected', this.menuItems[index])

      // 3秒后清除选择状态
      setTimeout(() => {
        this.selectedItem = null
      }, 3000)

      console.log('选中菜单项:', this.menuItems[index].label)
    },

    drawIconShape(p, iconClass, size) {
      // 根据图标类型绘制简化的几何形状
      p.push()

      switch(iconClass) {
        case 'fas fa-car':
          // 汽车图标 - 矩形
          p.rect(-size/2, -size/3, size, size/1.5, 3)
          break
        case 'fas fa-utensils':
          // 餐具图标 - 叉子形状
          p.line(0, -size/2, 0, size/2)
          p.line(-size/4, -size/3, size/4, -size/3)
          break
        case 'fas fa-map':
          // 地图图标 - 菱形
          p.beginShape()
          p.vertex(0, -size/2)
          p.vertex(size/2, 0)
          p.vertex(0, size/2)
          p.vertex(-size/2, 0)
          p.endShape(p.CLOSE)
          break
        case 'fas fa-music':
          // 音乐图标 - 音符
          p.circle(size/4, size/4, size/3)
          p.line(size/4 + size/6, size/4, size/4 + size/6, -size/2)
          break
        case 'fas fa-graduation-cap':
          // 学士帽图标 - 三角形
          p.triangle(0, -size/2, -size/2, size/3, size/2, size/3)
          break
        case 'fas fa-cog':
          // 齿轮图标 - 八角形
          p.beginShape()
          for (let i = 0; i < 8; i++) {
            const angle = (360 / 8) * i
            const r = i % 2 === 0 ? size/2 : size/3
            const x = p.cos(p.radians(angle)) * r
            const y = p.sin(p.radians(angle)) * r
            p.vertex(x, y)
          }
          p.endShape(p.CLOSE)
          break
        case 'fas fa-shield-alt':
          // 盾牌图标 - 盾形
          p.beginShape()
          p.vertex(0, -size/2)
          p.vertex(size/3, -size/3)
          p.vertex(size/3, size/4)
          p.vertex(0, size/2)
          p.vertex(-size/3, size/4)
          p.vertex(-size/3, -size/3)
          p.endShape(p.CLOSE)
          break
        case 'fas fa-comments':
          // 对话图标 - 圆角矩形
          p.rect(-size/2, -size/3, size, size/1.5, size/4)
          break
        default:
          // 默认图标 - 圆形
          p.circle(0, 0, size)
      }

      p.pop()
    }
  }
}
</script>
