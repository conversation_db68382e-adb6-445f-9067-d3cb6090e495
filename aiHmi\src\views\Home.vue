<template>
  <f7-page class="bg-gradient-to-br from-tech-dark to-tech-gray min-h-screen">
    <WheelMenu @menu-selected="onMenuSelected" />
    
    <!-- 状态显示 -->
    <div class="absolute top-4 left-4 z-20 max-w-xs">
      <div class="bg-black bg-opacity-50 backdrop-blur-sm rounded-lg p-4 text-white border border-tech-blue border-opacity-30">
        <h3 class="text-lg font-bold mb-2 neon-text">AI-HMI 控制中心</h3>
        <p class="text-sm opacity-75">点击或拖拽转盘菜单项进行交互</p>
        <div v-if="selectedItem" class="mt-2 p-2 bg-tech-blue bg-opacity-20 rounded border border-tech-blue border-opacity-50">
          <p class="text-sm font-medium">已选择: {{ selectedItem.label }}</p>
          <div class="flex items-center mt-1">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
            <span class="text-xs opacity-75">系统响应中...</span>
          </div>
        </div>

        <!-- 系统状态指示器 -->
        <div class="mt-3 flex items-center justify-between text-xs">
          <span class="opacity-75">系统状态</span>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-1"></div>
            <span class="text-green-400">在线</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右上角装饰和信息 -->
    <div class="absolute top-4 right-4 z-20 flex flex-col items-end space-y-4">
      <div class="w-16 h-16 border-2 border-tech-blue rounded-full animate-spin-slow opacity-50 relative">
        <div class="absolute inset-2 border border-neon-purple rounded-full animate-spin-slow opacity-30" style="animation-direction: reverse;"></div>
      </div>

      <!-- 时间显示 -->
      <div class="bg-black bg-opacity-50 backdrop-blur-sm rounded-lg px-3 py-2 text-white border border-tech-blue border-opacity-30">
        <div class="text-sm font-mono neon-text">{{ currentTime }}</div>
      </div>
    </div>

    <!-- 底部装饰和导航提示 -->
    <div class="absolute bottom-0 left-0 right-0 z-20">
      <div class="h-1 bg-gradient-to-r from-transparent via-tech-blue to-transparent opacity-50"></div>
      <div class="text-center py-4">
        <p class="text-white text-sm opacity-50">拖拽转盘旋转 • 点击选择功能 • 科技改变生活</p>
      </div>
    </div>
  </f7-page>
</template>

<script>
import WheelMenu from '../components/WheelMenu.vue'

export default {
  name: 'Home',
  components: {
    WheelMenu
  },
  data() {
    return {
      selectedItem: null,
      currentTime: ''
    }
  },
  mounted() {
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeUnmount() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    onMenuSelected(item) {
      this.selectedItem = item
      console.log('主页接收到选择事件:', item)

      // 这里可以添加路由跳转或其他逻辑
      // this.$router.push(`/${item.route}`)
    },

    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>
